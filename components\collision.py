"""
Bridge Collision Module

This module handles the creation of collision elements between:
1. Deck nodes and abutment nodes at each abutment 
2. Adjacent main beam nodes at pier locations

Collision elements are implemented as zero-length elements with high stiffness in the axial direction.
"""

import openseespy.opensees as ops
import numpy as np


def add_collision_elements(model):
    """
    Add collision elements between deck nodes and abutment nodes at each abutment.

    The collision stiffness is set to the main beam's axial stiffness.
    """
    # Check if abutment nodes exist
    if len(model.abutments['nodes']) != 2:
        print("Warning: Expected 2 abutment nodes, found", len(model.abutments['nodes']))
        return

    # Calculate main beam's axial stiffness
    axial_stiffness = calculate_beam_axial_stiffness(model)
    print(f"Using collision stiffness: {axial_stiffness/1e6:.2f} MN/m")

    # Create collision material
    gap = model.params.abutment['gap']*0
    collision_mat_left = model.mat_tags['CollisionLeft']
    ops.uniaxialMaterial(
        'ElasticPPGap', collision_mat_left,
        axial_stiffness, 1e10, gap, 0)

    collision_mat_right = model.mat_tags['CollisionRight']
    ops.uniaxialMaterial(
        'ElasticPPGap', collision_mat_right,
        axial_stiffness, -1e10, -gap, 0)

    # Create small restoring spring material to prevent numerical drift
    restoring_stiffness = axial_stiffness / 1000
    restoring_mat = model.mat_tags['RestoringGap']
    ops.uniaxialMaterial('Elastic', restoring_mat, restoring_stiffness)

    # Add collision elements at left abutment
    left_abutment_node = model.abutments['nodes'][0]
    add_abutment_collision(model, left_abutment_node, collision_mat_left, restoring_mat, 'left')

    # Add collision elements at right abutment
    right_abutment_node = model.abutments['nodes'][1]
    add_abutment_collision(model, right_abutment_node, collision_mat_right, restoring_mat, 'right')

    # Add collision elements between adjacent main beams at pier locations
    gap = model.params.abutment['gap']/2
    gap = np.maximum(gap, 0.02)
    gap = np.minimum(gap, 0.04)
    add_pier_collision_elements(model, axial_stiffness, gap, restoring_mat)


def add_abutment_collision(model, abutment_node, collision_mat_tag, restoring_mat_tag, side):
    """
    Add collision elements and restoring spring between deck node and abutment node

    Args:
        model: Bridge model object
        abutment_node: Node tag of the abutment node
        collision_mat_tag: Material tag for collision element
        restoring_mat_tag: Material tag for restoring spring element
        side: 'left' or 'right' to indicate which abutment
    """
    # Find deck nodes at this abutment
    if side == 'left':
        span_idx = 0
        deck_node = model.span_nodes[span_idx]['start']
    else:  # side == 'right'
        span_idx = max(model.span_nodes.keys())
        deck_node = model.span_nodes[span_idx]['end']

    # Create collision element
    elem_tag = model._next_tag('element')
    ops.element('zeroLength', elem_tag,
                deck_node, abutment_node,
                '-mat', collision_mat_tag,
                '-dir', 1)
    model.collision['elements'].append(elem_tag)

    # Create restoring spring element to prevent numerical drift
    restoring_elem_tag = model._next_tag('element')
    ops.element('zeroLength', restoring_elem_tag,
                deck_node, abutment_node,
                '-mat', restoring_mat_tag,
                '-dir', 1)
    model.collision['elements'].append(restoring_elem_tag)


def add_pier_collision_elements(model, axial_stiffness, gap, restoring_mat):
    """
    Add collision elements between adjacent main beam nodes at pier locations.

    Args:
        model: Bridge model object
        axial_stiffness: Axial stiffness for collision elements (N/m)
        gap: Gap distance for collision elements (m)
        restoring_mat: Material tag for restoring spring
    """
    # Create collision materials for girder collision elements (bidirectional)
    collision_mat = model.mat_tags['CollisionGirder']
    ops.uniaxialMaterial(
        'ElasticPPGap', collision_mat,
        axial_stiffness, 1e10, gap/2, 0)

    # Loop through each pier location
    for pier_long_idx in model.cap_beams['nodes'].keys():
        # Get adjacent spans - pier_long_idx is between spans pier_long_idx and pier_long_idx+1
        span_before = pier_long_idx
        span_after = pier_long_idx + 1

        # Skip if either span doesn't exist
        if span_before not in model.span_nodes or span_after not in model.span_nodes:
            continue

        # Get end node of span before and start node of span after
        end_node_before = model.span_nodes[span_before]['end']
        start_node_after = model.span_nodes[span_after]['start']

        # Create collision element for positive direction (span_after pushing span_before)
        elem_tag_pos = model._next_tag('element')
        ops.element('zeroLength', elem_tag_pos,
                    start_node_after, end_node_before,
                    '-mat', collision_mat,
                    '-dir', 1)
        model.collision['elements'].append(elem_tag_pos)

        # Create collision element for negative direction (span_before pushing span_after)
        elem_tag_neg = model._next_tag('element')
        ops.element('zeroLength', elem_tag_neg,
                    end_node_before, start_node_after,
                    '-mat', collision_mat,
                    '-dir', 1)
        model.collision['elements'].append(elem_tag_neg)

        # Create restoring spring element to prevent numerical drift
        restoring_elem_tag = model._next_tag('element')
        ops.element('zeroLength', restoring_elem_tag,
                    start_node_after, end_node_before,
                    '-mat', restoring_mat,
                    '-dir', 1)
        model.collision['elements'].append(restoring_elem_tag)


def calculate_beam_axial_stiffness(model):
    """
    Calculate the axial stiffness of the main beam.

    Returns:
        float: Axial stiffness in N/m
    """
    # Get section properties
    girder_type = model.params.girder
    section_params = model.params.girder_section[girder_type]

    # Get concrete elastic modulus
    E = model.params.concrete_materials['core']['Ec'] * 1e6  # Convert MPa to Pa

    # Calculate cross-sectional area based on girder type
    if girder_type == 'box':
        # Box girder: calculate area from dimensions
        width = model.params.deck_width
        height = section_params['height']
        top_thick = section_params['top_slab_thickness']
        bottom_thick = section_params['bottom_slab_thickness']
        web_thick = section_params['web_thickness']

        # Calculate cross-sectional area
        area = (width * top_thick) + (width * bottom_thick) + (2 * web_thick * (height - top_thick - bottom_thick))

    elif girder_type == 'hollow_slab':
        # Hollow slab: calculate area from dimensions
        width = model.params.deck_width
        slab_height = section_params['height']
        slab_width = section_params['slab_width']
        hollow_width = section_params['hollow_width']
        hollow_height = section_params['hollow_height']

        # Calculate cross-sectional area
        area = slab_width * slab_height - hollow_width * hollow_height
        area *= width // slab_width

    # Calculate axial stiffness (EA/L)
    l = np.min(model.params.span_lengths)
    axial_stiffness = E * area / l

    return axial_stiffness * 0.3
